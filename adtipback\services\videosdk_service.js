const jwt = require('jsonwebtoken');
require('dotenv').config();
const {default :fetch} = require('node-fetch'); // Ensure node-fetch is installed

class VideoSDKService {
  static generateToken() {
    const API_KEY = process.env.VIDEOSDK_API_KEY;
    const SECRET = process.env.VIDEOSDK_SECRET_KEY;

    if (!API_KEY || !SECRET) {
      throw new Error('API_KEY and SECRET are required in environment variables');
    }

    const options = {
      expiresIn: '30m',
      algorithm: 'HS256'
    };

    const payload = {
      apikey: API_KEY,
      permissions: ['allow_join', 'allow_mod']
      // Removed version, roomId, peerId, and roles
    };

    try {
      return jwt.sign(payload, SECRET, options);
    } catch (error) {
      throw new Error(`Token generation failed: ${error.message}`);
    }
  }

  static async createMeeting(token, region) {
    const url = `${process.env.VIDEOSDK_API_ENDPOINT}/rooms`;
    const options = {
      method: 'POST',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ region })
    };

    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      throw new Error(`Meeting creation failed: ${error.message}`);
    }
  }

  static async validateMeeting(token, meetingId) {
    const url = `${process.env.VIDEOSDK_API_ENDPOINT}/rooms/validate/${meetingId}`;
    const options = {
      method: 'GET',
      headers: { Authorization: token }
    };

    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      throw new Error(`Meeting validation failed: ${error.message}`);
    }
  }

  static async deactivateRoom(roomId, token = null) {
    // Generate token if not provided
    if (!token) {
      token = this.generateToken();
    }

    const url = `${process.env.VIDEOSDK_API_ENDPOINT}/rooms/deactivate`;
    const options = {
      method: 'POST',
      headers: {
        Authorization: token,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ roomId })
    };

    try {
      console.log(`[VideoSDKService] Deactivating room: ${roomId}`);
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      const result = await response.json();
      console.log(`[VideoSDKService] Room ${roomId} deactivated successfully`);
      return result;
    } catch (error) {
      console.error(`[VideoSDKService] Room deactivation failed for ${roomId}:`, error.message);
      throw new Error(`Room deactivation failed: ${error.message}`);
    }
  }

  /**
   * Get room participants count
   */
  static async getRoomParticipants(roomId, token = null) {
    if (!token) {
      token = this.generateToken();
    }

    const url = `${process.env.VIDEOSDK_API_ENDPOINT}/rooms/${roomId}/participants`;
    const options = {
      method: 'GET',
      headers: { Authorization: token }
    };

    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      const result = await response.json();
      return result.data || [];
    } catch (error) {
      console.error(`[VideoSDKService] Failed to get participants for room ${roomId}:`, error.message);
      throw new Error(`Get participants failed: ${error.message}`);
    }
  }

  /**
   * Check if room is empty and auto-cleanup if needed
   */
  static async checkAndCleanupEmptyRoom(roomId, token = null) {
    try {
      console.log(`[VideoSDKService] Checking room ${roomId} for auto-cleanup`);

      const participants = await this.getRoomParticipants(roomId, token);
      const participantCount = participants.length;

      console.log(`[VideoSDKService] Room ${roomId} has ${participantCount} participants`);

      if (participantCount === 0) {
        console.log(`[VideoSDKService] Room ${roomId} is empty - deactivating for billing optimization`);
        await this.deactivateRoom(roomId, token);
        return { deactivated: true, reason: 'empty_room' };
      }

      return { deactivated: false, participantCount };
    } catch (error) {
      console.error(`[VideoSDKService] Auto-cleanup check failed for room ${roomId}:`, error.message);
      return { deactivated: false, error: error.message };
    }
  }
}

module.exports = VideoSDKService;