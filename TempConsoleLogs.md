[DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
PersistentMeetingManager.tsx:484 [PersistentMeetingManager] Starting new call: a88836b4-f6ba-4d5f-873a-4e652f0230ca
PersistentMeetingManager.tsx:509 [PersistentMeetingManager] Persistent call started successfully
ProductionLogger.ts:117 [CALL:CallController] Making async consolidated call API request {callerId: 58422, receiverId: 58463, callType: 'voice', platform: 'ANDROID'}
TipCallScreenSimple.tsx:842 📞 [TipCallScreenSimple] Call initiation result: {success: true, recipientId: '58463', callType: 'voice', timestamp: '2025-07-29T08:59:40.021Z'}
TipCallScreenSimple.tsx:850 ✅ [TipCallScreenSimple] Call started successfully - API calls should be logged by CallController
ApiService.ts:246 Request to protected endpoint: /api/adtipcall. Attempting to add Authorization header.
console.js:654 Bluetooth Connect Permission Granted
ApiService.ts:253 Authorization header added to request for: /api/adtipcall
ApiService.ts:260 🚀 API REQUEST: {method: 'POST', url: '/api/adtipcall', baseURL: 'http://*************:7082', fullURL: 'http://*************:7082/api/adtipcall', headers: {…}, params: undefined, data: {…}, timeout: 60000, timestamp: '2025-07-29T08:59:40.032Z'}
PersistentMeetingManager.tsx:255 [PersistentMeetingContent] Setting meeting reference for session: a88836b4-f6ba-4d5f-873a-4e652f0230ca
MediaService.ts:232 [MediaService] Meeting reference set: true (meetingId: undefined)
PersistentMeetingManager.tsx:269 [PersistentMeetingContent] Join attempt 1 for session: a88836b4-f6ba-4d5f-873a-4e652f0230ca
PersistentMeetingManager.tsx:272 [PersistentMeetingContent] Joining meeting with ID: temp-a88836b4-f6ba-4d5f-873a-4e652f0230ca
PersistentMeetingManager.tsx:275 [PersistentMeetingContent] Successfully joined meeting
CallController.ts:73 [CallController] Status changed: connecting -> in_call
ProductionLogger.ts:51 [DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
ProductionLogger.ts:96 [NET:ApiService] POST http://*************:7082/api/adtipcall (200) {statusText: undefined, headers: {…}, data: {…}, timestamp: '2025-07-29T08:59:40.459Z'}
ProductionLogger.ts:117 [CALL:CallController] Async consolidated call API response {success: true, sessionId: '44db27fb-e51c-4954-a96e-26fd56198051', meetingId: 'vw1o-sx4m-f4xr'}
ProductionLogger.ts:117 [CALL:CallController] Updating session with real API data {apiSessionId: '44db27fb-e51c-4954-a96e-26fd56198051', meetingId: 'vw1o-sx4m-f4xr', backendCallId: 54}
ProductionLogger.ts:117 [CALL:CallController] Session updated successfully with real API data undefined
ProductionLogger.ts:51 [DEBUG:UltraFastLoader] ✅ Rendering MainNavigator for authenticated user
RectangleAdComponent.tsx:47 Rectangle ad loaded successfully
&platform=android&dev=true&lazy=true&minify=false&app=com.adtip.app.adtip_app&modulesOnly=false&runModule=true&excludeSource=true&sourcePaths=url-server:426017 Error while trying to reconnect websocket error


❌ API ERROR RESPONSE: {method: 'POST', url: '/api/voice-call', baseURL: 'http://*************:7082', fullURL: 'http://*************:7082/api/voice-call', status: 400, statusText: undefined, headers: {…}, data: {…}, timestamp: '2025-07-29T09:01:04.888Z'}baseURL: "http://*************:7082"data: {status: false, statusCode: 400, message: 'VideoSDK call record not found.'}fullURL: "http://*************:7082/api/voice-call"headers: {access-control-allow-credentials: 'true', access-control-allow-headers: 'Origin, X-Requested-With, Content-Type, Accept', access-control-allow-methods: 'GET, POST, PUT, DELETE', access-control-allow-origin: '*', connection: 'keep-alive', content-length: '77', content-security-policy: "default-src 'self';base-uri 'self';font-src 'self' https: data:;form-action 'self';frame-ancestors 'self';img-src 'self' data:;object-src 'none';script-src 'self';script-src-attr 'none';style-src 'self' https: 'unsafe-inline';upgrade-insecure-requests", content-type: 'application/json; charset=utf-8', cross-origin-embedder-policy: 'require-corp', cross-origin-opener-policy: 'same-origin', …}method: "POST"status: 400statusText: undefinedtimestamp: "2025-07-29T09:01:04.888Z"url: "/api/voice-call"[[Prototype]]: Object
ApiService.ts:633 API Error Details (handleError): {isAxiosError: true, status: 400, statusText: undefined, data: {…}, message: 'Request failed with status code 400', config: {…}}
CallController.ts:882 [CallController] ❌ End call API error: Error: VideoSDK call record not found.



[CallController] Status changed: in_call -> ended
NotificationService.ts:151 [NotificationService] Hiding notification: a88836b4-f6ba-4d5f-873a-4e652f0230ca
PersistentMeetingManager.tsx:420 [PersistentMeetingManager] Resetting call state
PersistentMeetingManager.tsx:212 [PersistentMeetingContent] Resetting for new call: a88836b4-f6ba-4d5f-873a-4e652f0230ca
PersistentMeetingManager.tsx:216 [PersistentMeetingContent] Stopping all media streams before reset
MediaService.ts:232 [MediaService] Meeting reference set: false 
PersistentMeetingManager.tsx:244 [PersistentMeetingContent] 