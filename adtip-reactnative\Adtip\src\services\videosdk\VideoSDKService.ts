import { register } from '@videosdk.live/react-native-sdk';
import ApiService from '../ApiService';
import { logVideoSDK, logError, logWarn } from '../../utils/ProductionLogger';

export interface VideoSDKConfig {
  token?: string;
  apiKey?: string;
  region?: 'sg001' | 'us001' | 'eu001';
}

export interface MeetingConfig {
  meetingId: string;
  token: string;
  participantName: string;
  micEnabled?: boolean;
  webcamEnabled?: boolean;
}

class VideoSDKService {
  private static instance: VideoSDKService;
  private isInitialized: boolean = false;
  private initializationPromise: Promise<boolean> | null = null;
  private config: VideoSDKConfig = {};
  
  // Add active meeting session tracking
  private activeMeetingSession: string | null = null;
  private meetingStateCleanupTimestamp: number = 0;

  private constructor() {}

  public static getInstance(): VideoSDKService {
    if (!VideoSDKService.instance) {
      VideoSDKService.instance = new VideoSDKService();
    }
    return VideoSDKService.instance;
  }

  /**
   * Initialize VideoSDK with proper WebSocket connection handling
   */
  async initialize(): Promise<boolean> {
    // If already initializing, return the existing promise
    if (this.initializationPromise) {
      return this.initializationPromise;
    }
    
    // If already initialized, return immediately
    if (this.isInitialized) {
      logVideoSDK('VideoSDKService', 'Already initialized');
      return true;
    }
    
    // Create a new initialization promise
    this.initializationPromise = (async () => {
      try {
        logVideoSDK('VideoSDKService', 'Initializing...');
        
        // Register with VideoSDK
        await register();

        // Add a longer delay to ensure WebSocket connection is fully established
        // This is especially important for the first call after app launch
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        this.isInitialized = true;
        logVideoSDK('VideoSDKService', 'Initialization complete');
        return true;
      } catch (error) {
        logError('VideoSDKService', 'Initialization failed', error);
        this.isInitialized = false;
        return false;
      } finally {
        this.initializationPromise = null;
      }
    })();
    
    return this.initializationPromise;
  }

  /**
   * Get initialization status
   */
  getInitializationStatus(): boolean {
    return this.isInitialized;
  }

  /**
   * Ensure VideoSDK is initialized with WebSocket ready
   */
  async ensureInitialized(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    return this.initialize();
  }

  /**
   * Wait for WebSocket connection to be fully ready with enhanced error handling
   * This is especially important for the first call after app launch
   */
  async waitForWebSocketReady(maxWaitMs: number = 5000): Promise<boolean> {
    if (!this.isInitialized) {
      logWarn('VideoSDKService', 'Cannot wait for WebSocket - VideoSDK not initialized');
      return false;
    }

    logVideoSDK('VideoSDKService', 'Waiting for WebSocket connection to be ready...');

    // Enhanced progressive delays with exponential backoff
    const delays = [500, 1000, 1500, 2000]; // Progressive delays up to 5 seconds total
    let totalWait = 0;

    for (const delay of delays) {
      if (totalWait + delay > maxWaitMs) {
        logWarn('VideoSDKService', `WebSocket wait timeout reached (${maxWaitMs}ms)`);
        break;
      }

      await new Promise(resolve => setTimeout(resolve, delay));
      totalWait += delay;

      logVideoSDK('VideoSDKService', `WebSocket readiness check - waited ${totalWait}ms total`);
    }

    logVideoSDK('VideoSDKService', 'WebSocket should be ready now');
    return true;
  }

  /**
   * Enhanced WebSocket reconnection with error handling
   */
  async handleWebSocketReconnection(error: any, attempt: number = 1, maxAttempts: number = 3): Promise<boolean> {
    logWarn('VideoSDKService', `WebSocket reconnection attempt ${attempt}/${maxAttempts}: ${error?.message || error}`);

    if (attempt > maxAttempts) {
      logError('VideoSDKService', 'Max WebSocket reconnection attempts reached');
      return false;
    }

    try {
      // Progressive delay with exponential backoff
      const baseDelay = 1000;
      const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), 10000); // Max 10 seconds

      logVideoSDK('VideoSDKService', `Waiting ${delay}ms before reconnection attempt ${attempt}`);
      await new Promise(resolve => setTimeout(resolve, delay));

      // Re-initialize VideoSDK to establish fresh WebSocket connection
      this.isInitialized = false;
      this.initializationPromise = null;

      const success = await this.initialize();

      if (success) {
        logVideoSDK('VideoSDKService', `WebSocket reconnection attempt ${attempt} successful`);
        // Additional wait to ensure connection stability
        await this.waitForWebSocketReady();
        return true;
      } else {
        logWarn('VideoSDKService', `WebSocket reconnection attempt ${attempt} failed`);
        return this.handleWebSocketReconnection(error, attempt + 1, maxAttempts);
      }
    } catch (reconnectError) {
      logError('VideoSDKService', `Error during reconnection attempt ${attempt}`, reconnectError);
      return this.handleWebSocketReconnection(reconnectError, attempt + 1, maxAttempts);
    }
  }

  /**
   * Get current configuration
   */
  public getConfig(): VideoSDKConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<VideoSDKConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logVideoSDK('VideoSDKService', 'Configuration updated', this.config);
  }  /**
   * Create a new meeting via backend API with state isolation
   */
  public async createMeeting(participantToken: string): Promise<string | null> {
    try {
      logVideoSDK('VideoSDKService', 'Creating meeting via backend API with state isolation');
      
      // First, clear any existing meeting state to prevent conflicts
      await this.clearExistingMeetingState();
      
      // Pass correct region code as per VideoSDK docs (us001, sg001, eu001)
      const response = await ApiService.createVideoSDKMeeting(participantToken, 'us001');
      
      logVideoSDK('VideoSDKService', 'Raw API response', response);
      
      // Fix: Check the correct response structure
      if (response.success && response.data && response.data.roomId) {
        logVideoSDK('VideoSDKService', 'Meeting created with isolation', response.data.roomId);
        return response.data.roomId;
      } else {
        logError('VideoSDKService', 'Invalid response structure', response);
        throw new Error('Failed to create meeting - invalid response structure');
      }
    } catch (error) {
      logError('VideoSDKService', 'Failed to create meeting', error);
      return null;
    }
  }

  /**
   * Validate meeting ID via backend API
   */
  public async validateMeeting(meetingId: string, participantToken: string): Promise<boolean> {
    try {
      logVideoSDK('VideoSDKService', 'Validating meeting via backend API', meetingId);
      
      // For now, assume meeting is valid if we have a meetingId
      // You can implement actual validation later if needed
      return !!meetingId;
    } catch (error) {
      logError('VideoSDKService', 'Failed to validate meeting', error);
      return false;
    }
  }
  /**
   * Generate participant token via backend API
   */
  public async generateParticipantToken(): Promise<string | null> {
    try {
      logVideoSDK('VideoSDKService', 'Generating participant token via backend');
      
      const response = await ApiService.generateVideoSDKToken();
      
      if (response.token) {
        return response.token;
      } else {
        throw new Error('Failed to generate token');
      }
    } catch (error) {
      logError('VideoSDKService', 'Failed to generate participant token', error);
      return null;
    }
  }
  /**
   * Generate meeting configuration
   */
  public createMeetingConfig(
    meetingId: string,
    token: string,
    participantName: string,
    options: { micEnabled?: boolean; webcamEnabled?: boolean } = {}
  ): MeetingConfig {
    return {
      meetingId,
      token,
      participantName,
      micEnabled: options.micEnabled ?? true, // Default to unmuted for better UX
      webcamEnabled: options.webcamEnabled ?? false, // Keep camera off by default for privacy
    };
  }

  /**
   * Force clear any existing meeting state before creating a new one
   * This prevents meeting ID conflicts and ensures clean state
   */
  public async clearExistingMeetingState(): Promise<void> {
    logVideoSDK('VideoSDKService', 'Clearing existing meeting state to prevent conflicts');
    
    try {
      // Clear any global meeting references
      if (global.videoSDKMeetingData) {
        global.videoSDKMeetingData = null;
      }
      
      // Clear active meeting session tracking
      if (this.activeMeetingSession) {
        logVideoSDK('VideoSDKService', 'Clearing previous active meeting session', this.activeMeetingSession);
        this.activeMeetingSession = null;
        this.meetingStateCleanupTimestamp = Date.now();
      }
      
      // Clear any participant data
      if (global.videoSDKParticipants) {
        global.videoSDKParticipants.clear();
      }
      
      // Clear VideoSDK internal state if accessible
      if (global.VideoSDK?.currentMeeting) {
        global.VideoSDK.currentMeeting = null;
      }
      
      // Force garbage collection to clear any remaining references
      if (global.gc) {
        global.gc();
      }
      
      // Add small delay to ensure cleanup is complete
      await new Promise(resolve => setTimeout(resolve, 100));
      
      logVideoSDK('VideoSDKService', 'Meeting state cleared successfully');
    } catch (error) {
      logWarn('VideoSDKService', 'Error clearing meeting state', error);
    }
  }

  /**
   * Reset service (for logout or cleanup)
   */
  public reset(): void {
    logVideoSDK('VideoSDKService', 'Starting comprehensive service reset');

    // Reset initialization state
    this.isInitialized = false;
    this.config = {};
    this.initializationPromise = null;

    // Force cleanup of any lingering WebRTC connections and participant state
    try {
      // Clear any global VideoSDK state if available
      if (global.VideoSDK) {
        logVideoSDK('VideoSDKService', 'Clearing global VideoSDK state');
        // Force cleanup of any active meetings or connections
      }

      // Clear any cached participant data that might cause state bleeding
      if (global.videoSDKParticipants) {
        logVideoSDK('VideoSDKService', 'Clearing cached participant data');
        global.videoSDKParticipants.clear();
        global.videoSDKParticipants = new Map();
      }

      // Clear any meeting data cache
      if (global.videoSDKMeetingData) {
        logVideoSDK('VideoSDKService', 'Clearing cached meeting data');
        global.videoSDKMeetingData = null;
      }

      // Force WebRTC cleanup to prevent participant ID conflicts
      if (global.RTCPeerConnection) {
        logVideoSDK('VideoSDKService', 'Forcing WebRTC connection cleanup');
        // This helps prevent participant ID conflicts between calls
      }

      // Clear any component instance tracking that might interfere
      if (global.meetingComponentInstances) {
        logVideoSDK('VideoSDKService', 'Clearing component instance tracking');
        global.meetingComponentInstances = {};
      }

      // Force clear React Native VideoSDK internal state
      try {
        // Clear any internal participant tracking that might cause ID conflicts
        if (global.VideoSDK?.participants) {
          global.VideoSDK.participants.clear();
        }
        
        // Reset any meeting session state
        if (global.VideoSDK?.currentMeeting) {
          global.VideoSDK.currentMeeting = null;
        }
        
        // Clear any WebSocket connection state
        if (global.VideoSDK?.websocketConnection) {
          global.VideoSDK.websocketConnection = null;
        }
      } catch (wsError) {
        logWarn('VideoSDKService', 'Error clearing WebSocket state', wsError);
      }

    } catch (error) {
      logWarn('VideoSDKService', 'Error during comprehensive state cleanup', error);
    }

    // Add delay to ensure all cleanup operations are complete
    setTimeout(() => {
      logVideoSDK('VideoSDKService', 'Service reset complete with delay');
    }, 100);

    logVideoSDK('VideoSDKService', 'Service reset complete');
  }

  /**
   * Set active meeting session to prevent multiple simultaneous meetings
   */
  public setActiveMeetingSession(sessionId: string): boolean {
    const now = Date.now();
    
    // If there's already an active session, check if it's the same or if enough time has passed for cleanup
    if (this.activeMeetingSession && this.activeMeetingSession !== sessionId) {
      // If the last cleanup was recent, don't allow new session
      if (now - this.meetingStateCleanupTimestamp < 2000) {
        logWarn('VideoSDKService', 'Another meeting session is active, rejecting new session', {
          active: this.activeMeetingSession,
          new: sessionId,
          timeSinceCleanup: now - this.meetingStateCleanupTimestamp
        });
        return false;
      }
    }
    
    logVideoSDK('VideoSDKService', 'Setting active meeting session', sessionId);
    this.activeMeetingSession = sessionId;
    return true;
  }
  
  /**
   * Clear active meeting session
   */
  public clearActiveMeetingSession(sessionId: string): void {
    if (this.activeMeetingSession === sessionId) {
      logVideoSDK('VideoSDKService', 'Clearing active meeting session', sessionId);
      this.activeMeetingSession = null;
      this.meetingStateCleanupTimestamp = Date.now();
    }
  }
  
  /**
   * Check if a meeting session is active
   */
  public isSessionActive(sessionId: string): boolean {
    return this.activeMeetingSession === sessionId;
  }
}

// ✅ FIXED: Export the class itself, not getInstance()
export default VideoSDKService;
